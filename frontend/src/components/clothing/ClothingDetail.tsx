'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import ExchangeOfferModal from '@/components/exchange/ExchangeOfferModal';
import { 
  Heart, 
  Eye, 
  MapPin, 
  Coins, 
  Recycle, 
  Gift,
  Star,
  User,
  Calendar,
  Tag,
  Shirt,
  ChevronLeft,
  ChevronRight,
  Share2,
  Flag,
  MessageCircle,
  ShoppingCart
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  brand?: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  originalPrice?: number;
  images: string[];
  views: number;
  likes: string[];
  likeCount: number;
  tags: string[];
  preferredSwapCategories?: string[];
  location: {
    county: string;
    town: string;
  };
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    rating: {
      average: number;
      count: number;
    };
    sustainabilityScore: number;
    location: {
      county: string;
      town: string;
    };
  };
  sustainabilityInfo: {
    material: string;
    careInstructions: string;
    estimatedLifespan: string;
    recyclingInfo?: string;
  };
  qualityAssurance?: {
    isVerified: boolean;
    verificationDate?: string;
    verifiedBy?: string;
    notes?: string;
  };
  createdAt: string;
  updatedAt: string;
  isAvailable: boolean;
}

interface ClothingDetailProps {
  item: ClothingItem;
  currentUserId?: string;
  onLike?: () => void;
  onContact?: () => void;
  onExchange?: () => void;
  loading?: boolean;
}

const getExchangeTypeIcon = (type: string) => {
  switch (type) {
    case 'swap':
      return <Recycle className="h-5 w-5" />;
    case 'token':
      return <Coins className="h-5 w-5" />;
    case 'donation':
      return <Gift className="h-5 w-5" />;
    default:
      return <Shirt className="h-5 w-5" />;
  }
};

const getExchangeTypeColor = (type: string) => {
  switch (type) {
    case 'swap':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'token':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'donation':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'all':
      return 'bg-gray-100 text-gray-800 border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getConditionColor = (condition: string) => {
  switch (condition) {
    case 'New':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200';
    case 'Like New':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'Good':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'Fair':
      return 'bg-orange-100 text-orange-800 border-orange-200';
    case 'Poor':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export default function ClothingDetail({
  item,
  currentUserId,
  onLike,
  onContact,
  onExchange,
  loading = false
}: ClothingDetailProps) {
  const router = useRouter();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(
    currentUserId ? item.likes.includes(currentUserId) : false
  );
  const [showExchangeModal, setShowExchangeModal] = useState(false);

  const handleLike = () => {
    if (onLike) {
      onLike();
      setIsLiked(!isLiked);
    }
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === item.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? item.images.length - 1 : prev - 1
    );
  };

  const isOwner = Boolean(currentUserId && currentUserId === item.owner._id);

  const handleExchange = () => {
    console.log('Exchange button clicked!', { exchangeType: item.exchangeType, currentUserId, isOwner });

    // Check if user is authenticated
    if (!currentUserId) {
      // Redirect to login page
      router.push('/auth/login');
      return;
    }

    setShowExchangeModal(true);
  };

  return (
    <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Image Gallery */}
      <div className="space-y-4">
        {/* Main Image */}
        <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
          <img
            src={item.images[currentImageIndex] || 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=400&h=400&fit=crop&crop=center'}
            alt={item.title}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=400&h=400&fit=crop&crop=center';
            }}
          />
          
          {/* Navigation Arrows */}
          {item.images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 transition-colors"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </>
          )}

          {/* Image Counter */}
          {item.images.length > 1 && (
            <div className="absolute bottom-3 right-3 bg-black/50 text-white px-2 py-1 rounded text-sm">
              {currentImageIndex + 1} / {item.images.length}
            </div>
          )}
        </div>

        {/* Thumbnail Gallery */}
        {item.images.length > 1 && (
          <div className="grid grid-cols-4 gap-2">
            {item.images.map((image, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`aspect-square rounded-lg overflow-hidden border-2 transition-colors ${
                  index === currentImageIndex 
                    ? 'border-green-500' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <img
                  src={image || 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=400&h=400&fit=crop&crop=center'}
                  alt={`${item.title} ${index + 1}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?w=400&h=400&fit=crop&crop=center';
                  }}
                />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Item Details */}
      <div className="space-y-6">
        {/* Header */}
        <div>
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{item.title}</h1>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  <span>{item.views} views</span>
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="h-4 w-4" />
                  <span>{item.likeCount} likes</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(item.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {currentUserId && !isOwner && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLike}
                  className={isLiked ? 'text-red-600 border-red-200' : ''}
                >
                  <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
                </Button>
              )}
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Flag className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Badges */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Badge className={`${getExchangeTypeColor(item.exchangeType)} flex items-center gap-1`}>
              {getExchangeTypeIcon(item.exchangeType)}
              {item.exchangeType === 'all' ? 'All Options' : item.exchangeType}
            </Badge>
            <Badge className={getConditionColor(item.condition)}>
              {item.condition}
            </Badge>
            <Badge variant="outline">{item.size}</Badge>
            <Badge variant="outline">{item.color}</Badge>
            {item.brand && <Badge variant="outline">{item.brand}</Badge>}
          </div>

          {/* Price */}
          {(item.exchangeType === 'token' || item.exchangeType === 'all') && item.tokenPrice && (
            <div className="mb-4">
              <div className="flex items-center gap-2">
                <Coins className="h-5 w-5 text-blue-600" />
                <span className="text-2xl font-bold text-blue-600">{item.tokenPrice} Tokens</span>
              </div>
              {item.originalPrice && (
                <p className="text-sm text-gray-500">
                  Original price: KES {item.originalPrice.toLocaleString()}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 whitespace-pre-wrap">{item.description}</p>
          </CardContent>
        </Card>

        {/* Tags */}
        {item.tags.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Tag className="h-5 w-5" />
                Tags
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {item.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    #{tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        {console.log('Button visibility check:', { isOwner, currentUserId, itemOwnerId: item.owner._id, isAvailable: item.isAvailable })}
        {!isOwner && (
          <div className="space-y-3">
            <Button
              onClick={handleExchange}
              className="w-full bg-brand-pine-green hover:bg-brand-pine-green/90 text-white"
              size="lg"
              disabled={!item.isAvailable || loading}
            >
              {item.exchangeType === 'donation' ? (
                <>
                  <Gift className="h-5 w-5 mr-2" />
                  Request Donation
                </>
              ) : item.exchangeType === 'token' ? (
                <>
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  Buy with Tokens
                </>
              ) : item.exchangeType === 'all' ? (
                <>
                  <Recycle className="h-5 w-5 mr-2" />
                  Make Offer
                </>
              ) : (
                <>
                  <Recycle className="h-5 w-5 mr-2" />
                  Propose Swap
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={onContact}
              className="w-full"
              disabled={loading}
            >
              <MessageCircle className="h-5 w-5 mr-2" />
              Contact Owner
            </Button>
          </div>
        )}

        {/* Debug info */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
            <p>Debug: isOwner={String(Boolean(isOwner))}, currentUserId={currentUserId || 'null'}, itemOwnerId={item.owner._id}</p>
            <p>Available: {String(Boolean(item.isAvailable))}, Exchange Type: {item.exchangeType}</p>
          </div>
        )}

        {!item.isAvailable && (
          <Alert>
            <AlertDescription>
              This item is no longer available for exchange.
            </AlertDescription>
          </Alert>
        )}

        {/* Owner Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Owner Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                {item.owner.profilePicture ? (
                  <img
                    src={item.owner.profilePicture}
                    alt={`${item.owner.firstName} ${item.owner.lastName}`}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <User className="h-6 w-6 text-gray-500" />
                )}
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">
                  {item.owner.firstName} {item.owner.lastName}
                </h3>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-yellow-400 fill-current" />
                    <span>{item.owner.rating.average.toFixed(1)} rating</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Recycle className="h-4 w-4 text-green-600" />
                    <span>{item.owner.sustainabilityScore} eco score</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    <span>{item.owner.location.county}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sustainability Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Recycle className="h-5 w-5 text-green-600" />
              Sustainability Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-1">Material</h4>
              <p className="text-gray-700">{item.sustainabilityInfo.material}</p>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-1">Care Instructions</h4>
              <p className="text-gray-700">{item.sustainabilityInfo.careInstructions}</p>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-1">Estimated Lifespan</h4>
              <p className="text-gray-700">{item.sustainabilityInfo.estimatedLifespan}</p>
            </div>

            {item.sustainabilityInfo.recyclingInfo && (
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Recycling Information</h4>
                <p className="text-gray-700">{item.sustainabilityInfo.recyclingInfo}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quality Assurance */}
        {item.qualityAssurance?.isVerified && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Badge className="bg-green-100 text-green-800">
                  ✓ Verified
                </Badge>
                Quality Assurance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                This item has been verified by our quality assurance team.
              </p>
              {item.qualityAssurance.verificationDate && (
                <p className="text-xs text-gray-500 mt-2">
                  Verified on {new Date(item.qualityAssurance.verificationDate).toLocaleDateString()}
                </p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Location */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700">
              {item.location.town}, {item.location.county}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Exchange Offer Modal */}
      {currentUserId && (
        <ExchangeOfferModal
          isOpen={showExchangeModal}
          onClose={() => setShowExchangeModal(false)}
          targetItem={item}
          currentUserId={currentUserId}
        />
      )}
    </div>
  );
}
